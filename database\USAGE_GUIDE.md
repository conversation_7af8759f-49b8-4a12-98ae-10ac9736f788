# Database Connection Usage Guide

This guide shows how to use the robust database connection system across your application modules.

## Quick Import Reference

```python
# For graceful degradation (recommended for most use cases)
from database.main import get_database_connection

# For strict error handling (critical operations)
from database.main import get_database_connection_strict, DatabaseConnectionError

# For transactions
from database.main import database_transaction

# For monitoring and utilities
from database.main import test_database_connection, get_database_info, close_database_connection
```

## Usage Patterns

### 1. Graceful Degradation Pattern (Recommended)

Use this pattern when your application can continue with reduced functionality if the database is unavailable:

```python
# In any module: services/user_service.py
from database.main import get_database_connection
import logging

logger = logging.getLogger(__name__)

async def get_user_profile(user_id: str):
    """Get user profile with graceful degradation."""
    db = await get_database_connection()
    
    if db is None:
        # Database unavailable - return cached data or default
        logger.warning("Database unavailable, returning default profile")
        return {"id": user_id, "name": "Unknown User", "cached": True}
    
    try:
        # Normal database operation
        user = await db.user.find_unique(where={"id": user_id})
        return user.model_dump() if user else None
    except Exception as e:
        logger.error(f"Database query failed: {e}")
        return None
```

### 2. Strict Error Handling Pattern

Use this pattern for critical operations that cannot proceed without database access:

```python
# In any module: services/payment_service.py
from database.main import get_database_connection_strict, DatabaseConnectionError
import logging

logger = logging.getLogger(__name__)

async def process_payment(payment_data: dict):
    """Process payment - requires database access."""
    try:
        db = await get_database_connection_strict()
        
        # Critical operation that must succeed
        payment = await db.payment.create(data=payment_data)
        logger.info(f"Payment processed: {payment.id}")
        return payment.model_dump()
        
    except DatabaseConnectionError as e:
        logger.critical(f"Payment failed - database unavailable: {e}")
        # Notify monitoring systems
        raise
    except Exception as e:
        logger.error(f"Payment processing failed: {e}")
        raise
```

### 3. Transaction Pattern

Use this pattern for operations that require multiple database changes to be atomic:

```python
# In any module: services/order_service.py
from database.main import database_transaction
import logging

logger = logging.getLogger(__name__)

async def create_order_with_items(order_data: dict, items: list):
    """Create order with items in a transaction."""
    try:
        async with database_transaction() as db:
            # Create order
            order = await db.order.create(data=order_data)
            
            # Create order items
            for item_data in items:
                item_data['order_id'] = order.id
                await db.order_item.create(data=item_data)
            
            # Update inventory
            for item in items:
                await db.product.update(
                    where={'id': item['product_id']},
                    data={'stock': {'decrement': item['quantity']}}
                )
            
            logger.info(f"Order created successfully: {order.id}")
            return order.model_dump()
            
    except Exception as e:
        logger.error(f"Order creation failed (rolled back): {e}")
        raise
```

### 4. Health Monitoring Pattern

Use this pattern for application health checks and monitoring:

```python
# In any module: services/health_service.py
from database.main import test_database_connection, get_database_info
import logging

logger = logging.getLogger(__name__)

async def health_check():
    """Comprehensive application health check."""
    health_status = {
        'status': 'healthy',
        'database': {
            'available': False,
            'connection_test': False,
            'details': {}
        },
        'timestamp': time.time()
    }
    
    try:
        # Test database connection
        health_status['database']['connection_test'] = await test_database_connection()
        health_status['database']['available'] = health_status['database']['connection_test']
        
        # Get detailed connection info
        health_status['database']['details'] = await get_database_info()
        
        if not health_status['database']['available']:
            health_status['status'] = 'degraded'
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        health_status['status'] = 'unhealthy'
        health_status['error'] = str(e)
    
    return health_status
```

## Application Integration Examples

### FastAPI Integration

```python
# main.py
from fastapi import FastAPI, HTTPException
from database.main import test_database_connection, close_database_connection
from services.user_service import get_user_profile
from services.health_service import health_check

app = FastAPI()

@app.on_event("startup")
async def startup_event():
    """Application startup - test database connection."""
    if await test_database_connection():
        logger.info("Database connection verified")
    else:
        logger.warning("Database unavailable - starting with degraded functionality")

@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown - cleanup database connections."""
    await close_database_connection()

@app.get("/users/{user_id}")
async def get_user(user_id: str):
    """Get user with graceful degradation."""
    user = await get_user_profile(user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@app.get("/health")
async def health():
    """Health check endpoint."""
    return await health_check()
```

### Flask Integration

```python
# app.py
from flask import Flask, jsonify
import asyncio
from database.main import test_database_connection, close_database_connection

app = Flask(__name__)

@app.before_first_request
def startup():
    """Test database connection on startup."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    if loop.run_until_complete(test_database_connection()):
        app.logger.info("Database connection verified")
    else:
        app.logger.warning("Database unavailable")

@app.teardown_appcontext
def shutdown(exception):
    """Cleanup on shutdown."""
    loop = asyncio.get_event_loop()
    loop.run_until_complete(close_database_connection())

@app.route('/health')
def health():
    """Health check endpoint."""
    loop = asyncio.get_event_loop()
    health_data = loop.run_until_complete(health_check())
    return jsonify(health_data)
```

## Environment Configuration

Create a `.env` file in your project root:

```bash
# Database connection settings
DB_MAX_RETRIES=3
DB_RETRY_DELAY=1.0
DB_CONNECTION_TIMEOUT=10.0
DB_HEALTH_CHECK_INTERVAL=30.0

# Logging
LOG_LEVEL=INFO
```

## Best Practices Summary

1. **Use graceful degradation** for non-critical features
2. **Use strict mode** for critical operations (payments, user auth, etc.)
3. **Use transactions** for multi-step operations
4. **Monitor database health** in production
5. **Handle DatabaseConnectionError** appropriately
6. **Configure timeouts** based on your environment
7. **Use proper logging** for debugging and monitoring
8. **Test connection** during application startup
9. **Cleanup connections** during shutdown
10. **Consider caching** for frequently accessed data when database is unavailable

## Testing Your Implementation

Run the provided test scripts to verify everything works:

```bash
# Test basic functionality
cd database
python main.py

# Test usage examples
python example_usage.py

# Test error handling
python test_error_handling.py
```

The database connection system is now ready for production use with comprehensive error handling, graceful degradation, and robust connection management!
