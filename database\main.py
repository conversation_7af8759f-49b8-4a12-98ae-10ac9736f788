import asyncio
import logging
import os
import time
from typing import Optional, Union
from contextlib import asynccontextmanager

from prisma import Prisma
from prisma.errors import PrismaError


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseConnectionError(Exception):
    """Custom exception for database connection failures."""

    def __init__(self, message: str, original_error: Optional[Exception] = None):
        self.message = message
        self.original_error = original_error
        super().__init__(self.message)


class DatabaseManager:
    """
    Singleton database manager for handling Prisma connections with robust error handling,
    connection pooling, and graceful degradation.
    """

    _instance: Optional['DatabaseManager'] = None
    _lock = asyncio.Lock()

    def __init__(self):
        self._client: Optional[Prisma] = None
        self._is_connected: bool = False
        self._connection_attempts: int = 0
        self._last_connection_attempt: float = 0
        self._max_retries: int = int(os.getenv('DB_MAX_RETRIES', '3'))
        self._retry_delay: float = float(os.getenv('DB_RETRY_DELAY', '1.0'))
        self._connection_timeout: float = float(os.getenv('DB_CONNECTION_TIMEOUT', '10.0'))
        self._health_check_interval: float = float(os.getenv('DB_HEALTH_CHECK_INTERVAL', '30.0'))
        self._last_health_check: float = 0

    @classmethod
    async def get_instance(cls) -> 'DatabaseManager':
        """Get singleton instance of DatabaseManager."""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    async def _create_client(self) -> Prisma:
        """Create a new Prisma client instance."""
        try:
            client = Prisma()
            logger.info("Created new Prisma client instance")
            return client
        except Exception as e:
            logger.error(f"Failed to create Prisma client: {e}")
            raise DatabaseConnectionError(
                "Failed to create database client",
                original_error=e
            )

    async def _connect_with_retry(self) -> bool:
        """Attempt to connect to database with exponential backoff retry logic."""
        for attempt in range(1, self._max_retries + 1):
            try:
                logger.info(f"Database connection attempt {attempt}/{self._max_retries}")

                if self._client is None:
                    self._client = await self._create_client()

                # Set connection timeout
                await asyncio.wait_for(
                    self._client.connect(),
                    timeout=self._connection_timeout
                )

                self._is_connected = True
                self._connection_attempts = 0
                logger.info("Successfully connected to database")
                return True

            except asyncio.TimeoutError:
                logger.warning(f"Database connection timeout on attempt {attempt}")
            except PrismaError as e:
                logger.warning(f"Prisma error on attempt {attempt}: {e}")
            except Exception as e:
                logger.warning(f"Unexpected error on attempt {attempt}: {e}")

            if attempt < self._max_retries:
                delay = self._retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                logger.info(f"Retrying connection in {delay} seconds...")
                await asyncio.sleep(delay)

            self._connection_attempts = attempt

        self._is_connected = False
        logger.error(f"Failed to connect to database after {self._max_retries} attempts")
        return False

    async def connect(self) -> Prisma:
        """
        Establish database connection with comprehensive error handling.

        Returns:
            Prisma: Connected database client

        Raises:
            DatabaseConnectionError: If connection fails after all retries
        """
        async with self._lock:
            current_time = time.time()
            self._last_connection_attempt = current_time

            # If already connected and healthy, return existing client
            if self._is_connected and self._client:
                if await self._health_check():
                    logger.debug("Using existing healthy database connection")
                    return self._client
                else:
                    logger.warning("Existing connection unhealthy, reconnecting...")
                    await self._disconnect()

            # Attempt to establish connection
            success = await self._connect_with_retry()

            if not success or not self._client:
                raise DatabaseConnectionError(
                    f"Failed to establish database connection after {self._max_retries} attempts"
                )

            return self._client

    async def _health_check(self) -> bool:
        """Perform health check on database connection."""
        if not self._client or not self._is_connected:
            return False

        current_time = time.time()

        # Skip health check if recently performed
        if current_time - self._last_health_check < self._health_check_interval:
            return True

        try:
            # Simple query to test connection
            await self._client.query_raw("SELECT 1")
            self._last_health_check = current_time
            logger.debug("Database health check passed")
            return True
        except Exception as e:
            logger.warning(f"Database health check failed: {e}")
            self._is_connected = False
            return False

    async def _disconnect(self) -> None:
        """Safely disconnect from database."""
        if self._client:
            try:
                await self._client.disconnect()
                logger.info("Disconnected from database")
            except Exception as e:
                logger.warning(f"Error during disconnect: {e}")
            finally:
                self._client = None
                self._is_connected = False

    async def disconnect(self) -> None:
        """Public method to disconnect from database."""
        async with self._lock:
            await self._disconnect()

    @property
    def is_connected(self) -> bool:
        """Check if database is currently connected."""
        return self._is_connected

    @property
    def connection_info(self) -> dict:
        """Get connection status information."""
        return {
            'is_connected': self._is_connected,
            'connection_attempts': self._connection_attempts,
            'last_connection_attempt': self._last_connection_attempt,
            'last_health_check': self._last_health_check,
            'max_retries': self._max_retries,
            'retry_delay': self._retry_delay,
            'connection_timeout': self._connection_timeout
        }


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


async def get_database_connection() -> Union[Prisma, None]:
    """
    Get database connection with comprehensive error handling.

    This is the main function to be used throughout the application for database access.
    It implements graceful degradation - returns None if connection fails, allowing
    the application to continue running with reduced functionality.

    Returns:
        Prisma: Database client if connection successful
        None: If connection fails (graceful degradation)
    """
    global _db_manager

    try:
        if _db_manager is None:
            _db_manager = await DatabaseManager.get_instance()

        return await _db_manager.connect()

    except DatabaseConnectionError as e:
        logger.error(f"Database connection failed: {e.message}")
        if e.original_error:
            logger.error(f"Original error: {e.original_error}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting database connection: {e}")
        return None


async def get_database_connection_strict() -> Prisma:
    """
    Get database connection with strict error handling.

    This function raises exceptions on connection failure, suitable for critical
    operations that cannot proceed without database access.

    Returns:
        Prisma: Database client

    Raises:
        DatabaseConnectionError: If connection fails
    """
    global _db_manager

    if _db_manager is None:
        _db_manager = await DatabaseManager.get_instance()

    return await _db_manager.connect()


@asynccontextmanager
async def database_transaction():
    """
    Context manager for database transactions with automatic rollback on error.

    Usage:
        async with database_transaction() as db:
            # Perform database operations
            await db.user.create(...)
    """
    db = await get_database_connection_strict()

    try:
        async with db.tx() as transaction:
            yield transaction
    except Exception as e:
        logger.error(f"Transaction failed, rolling back: {e}")
        raise


async def test_database_connection() -> bool:
    """
    Test database connection and return status.

    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        db = await get_database_connection()
        if db is None:
            return False

        # Perform a simple query to test connection
        await db.query_raw("SELECT 1")
        logger.info("Database connection test successful")
        return True

    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


async def get_database_info() -> dict:
    """
    Get database connection information and statistics.

    Returns:
        dict: Database connection information
    """
    global _db_manager

    if _db_manager is None:
        return {'status': 'not_initialized'}

    info = _db_manager.connection_info
    info['status'] = 'connected' if info['is_connected'] else 'disconnected'
    return info


async def close_database_connection() -> None:
    """
    Close database connection and cleanup resources.

    Should be called during application shutdown.
    """
    global _db_manager

    if _db_manager:
        await _db_manager.disconnect()
        logger.info("Database connection closed")


async def main() -> None:
    """Example usage and testing."""
    logger.info("Testing database connection...")

    # Test graceful degradation
    db = await get_database_connection()
    if db:
        logger.info("Database connection successful!")

        # Test health check
        connection_test = await test_database_connection()
        logger.info(f"Connection test result: {connection_test}")

        # Get connection info
        info = await get_database_info()
        logger.info(f"Connection info: {info}")

    else:
        logger.warning("Database connection failed, but application can continue with reduced functionality")

    # Cleanup
    await close_database_connection()


if __name__ == '__main__':
    asyncio.run(main())