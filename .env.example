# Database Connection Configuration
# Copy this file to .env and adjust values as needed

# Maximum number of connection retry attempts
DB_MAX_RETRIES=3

# Initial retry delay in seconds (uses exponential backoff)
DB_RETRY_DELAY=1.0

# Connection timeout in seconds
DB_CONNECTION_TIMEOUT=10.0

# Health check interval in seconds
DB_HEALTH_CHECK_INTERVAL=30.0

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Database URL (if different from schema.prisma)
# DATABASE_URL=file:./database/prisma/dev.db
