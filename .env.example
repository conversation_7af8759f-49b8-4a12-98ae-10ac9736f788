# Database Connection Configuration
# Copy this file to .env and adjust values as needed

# PostgreSQL Database URL
# Format: postgresql://username:password@host:port/database_name
# Examples:
#   Local development: postgresql://postgres:password@localhost:5432/meaning_db
#   Production: ***************************************/meaning_prod
#   With SSL: ********************************/db?sslmode=require
DATABASE_URL=postgresql://postgres:password@localhost:5432/meaning_db

# Alternative: Individual PostgreSQL connection parameters
# (DATABASE_URL takes precedence if both are set)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DATABASE=meaning_db
POSTGRES_SCHEMA=public

# SSL Configuration (optional)
# POSTGRES_SSL_MODE=prefer  # disable, allow, prefer, require, verify-ca, verify-full

# Connection Pool Settings
DB_CONNECTION_POOL_SIZE=10
DB_CONNECTION_POOL_TIMEOUT=30

# Database Connection Retry Configuration
DB_MAX_RETRIES=3
DB_RETRY_DELAY=1.0
DB_CONNECTION_TIMEOUT=30.0
DB_HEALTH_CHECK_INTERVAL=30.0

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Migration settings
AUTO_MIGRATE_ON_STARTUP=false
RESET_DATABASE_ON_STARTUP=false
