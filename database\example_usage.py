"""
Example usage of the database connection functions across different modules.

This module demonstrates how to import and use the database connection
functions in various scenarios throughout your application.
"""

import asyncio
import logging
from typing import List, Optional

from main import (
    get_database_connection,
    get_database_connection_strict,
    database_transaction,
    test_database_connection,
    get_database_info,
    close_database_connection,
    DatabaseConnectionError
)

logger = logging.getLogger(__name__)


class ElementService:
    """Example service class showing database usage patterns."""
    
    async def get_all_elements(self) -> Optional[List[dict]]:
        """
        Get all elements with graceful degradation.
        Returns None if database is unavailable.
        """
        db = await get_database_connection()
        if db is None:
            logger.warning("Database unavailable, returning empty result")
            return None
        
        try:
            elements = await db.element.find_many()
            return [element.dict() for element in elements]
        except Exception as e:
            logger.error(f"Error fetching elements: {e}")
            return None
    
    async def create_element_strict(self, element_data: dict) -> dict:
        """
        Create element with strict error handling.
        Raises exception if database is unavailable.
        """
        try:
            db = await get_database_connection_strict()
            element = await db.element.create(data=element_data)
            logger.info(f"Created element: {element.element_code}")
            return element.dict()
        except DatabaseConnectionError as e:
            logger.error(f"Database connection failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Error creating element: {e}")
            raise
    
    async def update_element_with_transaction(self, element_code: str, updates: dict) -> bool:
        """
        Update element using transaction context manager.
        """
        try:
            async with database_transaction() as db:
                # Update element
                await db.element.update(
                    where={'element_code': element_code},
                    data=updates
                )
                
                # You could perform additional related updates here
                # All will be rolled back if any operation fails
                
                logger.info(f"Updated element {element_code} successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error updating element {element_code}: {e}")
            return False


class HealthCheckService:
    """Service for monitoring database health."""
    
    async def check_database_health(self) -> dict:
        """Comprehensive database health check."""
        health_status = {
            'database_available': False,
            'connection_test': False,
            'connection_info': {},
            'timestamp': asyncio.get_event_loop().time()
        }
        
        try:
            # Test basic connection
            db = await get_database_connection()
            health_status['database_available'] = db is not None
            
            # Perform connection test
            health_status['connection_test'] = await test_database_connection()
            
            # Get detailed connection info
            health_status['connection_info'] = await get_database_info()
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            health_status['error'] = str(e)
        
        return health_status


async def example_application_startup():
    """Example of how to handle database connection during application startup."""
    logger.info("Starting application...")
    
    # Test database connection during startup
    if await test_database_connection():
        logger.info("Database connection verified during startup")
    else:
        logger.warning("Database connection failed during startup - continuing with degraded functionality")
    
    # Your application initialization code here
    logger.info("Application startup complete")


async def example_application_shutdown():
    """Example of how to handle database cleanup during application shutdown."""
    logger.info("Shutting down application...")
    
    # Close database connections
    await close_database_connection()
    
    logger.info("Application shutdown complete")


async def example_usage_patterns():
    """Demonstrate various usage patterns."""
    
    # Initialize services
    element_service = ElementService()
    health_service = HealthCheckService()
    
    logger.info("=== Database Connection Examples ===")
    
    # 1. Graceful degradation pattern
    logger.info("\n1. Testing graceful degradation pattern:")
    elements = await element_service.get_all_elements()
    if elements is not None:
        logger.info(f"Found {len(elements)} elements")
    else:
        logger.info("Database unavailable, using cached data or default behavior")
    
    # 2. Strict error handling pattern
    logger.info("\n2. Testing strict error handling pattern:")
    try:
        # This would fail if database is unavailable
        new_element = await element_service.create_element_strict({
            'element_code': 'TEST001',
            'element_value': 'Test Element',
            'element_realm': 'Testing'
        })
        logger.info(f"Created element: {new_element}")
    except DatabaseConnectionError:
        logger.error("Cannot create element - database connection required")
    except Exception as e:
        logger.error(f"Element creation failed: {e}")
    
    # 3. Transaction pattern
    logger.info("\n3. Testing transaction pattern:")
    success = await element_service.update_element_with_transaction(
        'TEST001',
        {'element_value': 'Updated Test Element'}
    )
    logger.info(f"Transaction update success: {success}")
    
    # 4. Health monitoring pattern
    logger.info("\n4. Testing health monitoring:")
    health = await health_service.check_database_health()
    logger.info(f"Database health: {health}")


async def main():
    """Main example function."""
    try:
        await example_application_startup()
        await example_usage_patterns()
    finally:
        await example_application_shutdown()


if __name__ == '__main__':
    # Configure logging for examples
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    asyncio.run(main())
