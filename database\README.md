# Database Connection Module

This module provides a robust, production-ready PostgreSQL database connection system with comprehensive error handling, connection pooling, and graceful degradation capabilities.

## Features

- **Robust Error Handling**: Prevents application crashes on database failures
- **Connection Pooling**: Efficient connection management with singleton pattern
- **Graceful Degradation**: Application continues running even when database is unavailable
- **Retry Logic**: Exponential backoff retry mechanism for connection failures
- **Health Monitoring**: Automatic connection health checks
- **Transaction Support**: Context manager for database transactions
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Environment Configuration**: Configurable through environment variables

## Quick Start

### Basic Usage (Graceful Degradation)

```python
from database.main import get_database_connection

async def my_function():
    db = await get_database_connection()
    if db is None:
        # Handle database unavailable case
        return default_data
    
    # Use database normally
    result = await db.element.find_many()
    return result
```

### Strict Usage (Raises Exceptions)

```python
from database.main import get_database_connection_strict, DatabaseConnectionError

async def critical_function():
    try:
        db = await get_database_connection_strict()
        result = await db.element.create(data=element_data)
        return result
    except DatabaseConnectionError:
        # Handle connection failure
        raise
```

### Transaction Usage

```python
from database.main import database_transaction

async def update_with_transaction():
    try:
        async with database_transaction() as db:
            await db.element.update(...)
            await db.element_being.create(...)
            # All operations committed together
    except Exception:
        # Automatic rollback on any error
        raise
```

## Available Functions

### Core Functions

- `get_database_connection()` - Returns database client or None (graceful degradation)
- `get_database_connection_strict()` - Returns database client or raises exception
- `database_transaction()` - Context manager for transactions
- `test_database_connection()` - Test connection health
- `get_database_info()` - Get connection statistics
- `close_database_connection()` - Cleanup connections

### Utility Functions

- `DatabaseConnectionError` - Custom exception class
- `DatabaseManager` - Singleton connection manager class

## Configuration

Configure the database connection behavior using environment variables:

```bash
# Maximum retry attempts
DB_MAX_RETRIES=3

# Initial retry delay (exponential backoff)
DB_RETRY_DELAY=1.0

# Connection timeout
DB_CONNECTION_TIMEOUT=10.0

# Health check interval
DB_HEALTH_CHECK_INTERVAL=30.0
```

## Error Handling Patterns

### Pattern 1: Graceful Degradation

Use when the application can continue with reduced functionality:

```python
async def get_user_data(user_id: str):
    db = await get_database_connection()
    if db is None:
        # Return cached data or default
        return get_cached_user_data(user_id)
    
    return await db.user.find_unique(where={'id': user_id})
```

### Pattern 2: Strict Requirements

Use when database access is critical:

```python
async def create_payment(payment_data: dict):
    try:
        db = await get_database_connection_strict()
        return await db.payment.create(data=payment_data)
    except DatabaseConnectionError:
        # Log error and notify administrators
        logger.critical("Payment creation failed - database unavailable")
        raise
```

### Pattern 3: Health Monitoring

```python
async def health_check():
    health = await get_database_info()
    if not health.get('is_connected'):
        # Alert monitoring systems
        send_alert("Database connection lost")
    return health
```

## Application Integration

### Startup

```python
async def app_startup():
    # Test database connection during startup
    if await test_database_connection():
        logger.info("Database ready")
    else:
        logger.warning("Database unavailable - starting with degraded functionality")
```

### Shutdown

```python
async def app_shutdown():
    # Clean up database connections
    await close_database_connection()
```

## Best Practices

1. **Use graceful degradation** for non-critical operations
2. **Use strict mode** for critical operations that require database access
3. **Always handle DatabaseConnectionError** in strict mode
4. **Use transactions** for multi-step operations
5. **Monitor connection health** in production
6. **Configure appropriate timeouts** for your environment
7. **Use proper logging** for debugging and monitoring

## Monitoring and Debugging

The module provides comprehensive logging at different levels:

- **INFO**: Connection status, successful operations
- **WARNING**: Connection retries, health check failures
- **ERROR**: Connection failures, operation errors
- **DEBUG**: Detailed connection information

Enable debug logging for troubleshooting:

```python
import logging
logging.getLogger('database.main').setLevel(logging.DEBUG)
```

## Example Service Implementation

See `database/example_usage.py` for complete examples of:

- Service classes using database connections
- Health monitoring services
- Application startup/shutdown patterns
- Various error handling strategies

## Testing

Test the database connection:

```bash
cd database
python main.py
```

Run example usage patterns:

```bash
python database/example_usage.py
```
