datasource db {
  provider = "sqlite"
  url      = "file:dev.db"
}

generator client {
  provider             = "prisma-client-py"
  interface            = "asyncio"
  recursive_type_depth = 5
}

model ElementBeing {
  being_code        String   @id
  element_code      String
  being_reality     String   // formula
  being_description String?

  element           Element  @relation(fields: [element_code], references: [element_code])
  meanings          ElementMeaning[]
}

model Element {
  element_code  String   @id
  element_value String
  element_realm String

  beings        ElementBeing[]
  meanings      ElementMeaning[]
  natures       ElementNature[]
  identities    ElementIdentity[]
}

model ElementMeaning {
  element_code String
  being_code   String
  nature_code  String
  identity_code String

  element      Element     @relation(fields: [element_code], references: [element_code])
  being        ElementBeing @relation(fields: [being_code], references: [being_code])
  nature       ElementNature @relation(fields: [nature_code], references: [nature_code])
  identity     ElementIdentity @relation(fields: [identity_code], references: [identity_code])

  @@id([element_code, being_code, nature_code, identity_code])
}

model ElementNature {
  nature_code        String   @id
  element_code       String
  nature_reality     String   // formula
  nature_description String?

  element            Element  @relation(fields: [element_code], references: [element_code])
  meanings           ElementMeaning[]
}

model ElementIdentity {
  identity_code       String   @id
  element_code        String
  identity_reality    String   // formula
  identity_description String?

  element             Element  @relation(fields: [element_code], references: [element_code])
  meanings            ElementMeaning[]
}

model SymbolMeaning {
  symean_code     String   @id
  sense_code      String
  symbol_code     String
  formula_being   String   // nature of being
  formula_state   String   // state of being
  formula_mean    String   // meaning of being

  sense           Sense     @relation(fields: [sense_code], references: [sense_code])
  symbol          Symbol    @relation(fields: [symbol_code], references: [symbol_code])
}

model SenseMeaning {
  sensmean_code  String   @id
  sense_code     String
  sense_matter   String
  sense_point    String
  sense_language String

  sense          Sense     @relation(fields: [sense_code], references: [sense_code])
}

model Symbol {
  symbol_code     String   @id
  symbol_task     String
  symbol_notation String

  meanings        SymbolMeaning[]
}

model Sense {
  sense_code     String   @id
  sense_value    String
  sense_dimension String

  symbol_meanings SymbolMeaning[]
  sense_meanings  SenseMeaning[]
}